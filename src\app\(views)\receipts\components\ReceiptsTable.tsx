import { useState, useEffect, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { formatDateTime } from '@/app/shared/utils/dates.utils';
import {
  updateItem,
  fetchAssets,
  fetchVendors,
  fetchVessels,
} from '@/app/(views)/receipts/receipts.services';
import { PackageType } from '@/app/shared/models/global.enums';
import { IItem } from '@/app/shared/models/global.interfaces';
import { KeyedMutator } from 'swr';
import { InputSwitch } from 'primereact/inputswitch';
import {
  textEditor,
  dateTimeEditor,
  numberEditor,
  dropdownEditor,
  multiSelectEditor,
} from '../utils/editors.utils';
import ReusableToast from '@/app/shared/components/ReusableToast';
import {
  getUpdateSuccessToast,
  getUpdateErrorToast,
} from './utils/ReceiptsDialogsSettings';
import { useWarehouseStore } from '@/store';
import PackageTypeTags from '@/app/shared/components/PackageTypeTags';

interface ReceiptsTableProps {
  filteredReceipts: IItem[];
  selectedReceipts: IItem[];
  setSelectedReceipts: (value: IItem[]) => void;
  globalFilter: string;
  refetchItems: KeyedMutator<IItem[]>;
  userRole?: string;
  visibleColumns?: any[];
  onColumnToggle?: (event: any) => void;
  warehouseId?: string;
}

// Define mandatory columns that are always visible
const mandatoryColumns = [{ field: 'ReceiptNumber', header: 'Receipt Number' }];

// Define toggleable columns for the table
const toggleableColumns = [
  { field: 'ReceivedByUserId', header: 'Received By' },
  { field: 'ReceiptDateTime', header: 'Receipt Date & Time' },
  { field: 'AssetId', header: 'Asset' },
  { field: 'VendorId', header: 'Vendor' },
  { field: 'PONumber', header: 'P/O Number' },
  {
    field: 'SupplierDeliveryNoteNumber',
    header: 'Supplier Delivery Note Number',
  },
  { field: 'SAPDelivery', header: 'SAP Delivery' },
  { field: 'NumberOfPackages', header: '# of Packages' },
  { field: 'packageType', header: 'Type of Package' },
  { field: 'NumberOfLineItems', header: '# of Line Items' },
  { field: 'DescriptionOfGoods', header: 'Description of Goods' },
  { field: 'IsQuarantined', header: 'Quarantined' },
  { field: 'LoadListItem', header: 'Load List' },
  { field: 'CCU', header: 'CCU' },
  { field: 'Manifest', header: 'Manifest' },
  { field: 'SailingDate', header: 'Sailing Date' },
  { field: 'VesselId', header: 'Vessel' },
];

// All columns combined
const allColumns = [...mandatoryColumns, ...toggleableColumns];

function ReceiptsTable({
  filteredReceipts,
  selectedReceipts,
  setSelectedReceipts,
  globalFilter,
  refetchItems,
  userRole,
  visibleColumns = allColumns,
  onColumnToggle,
  warehouseId,
}: ReceiptsTableProps) {
  // Lookup data states
  const [assets, setAssets] = useState<any[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [vessels, setVessels] = useState<any[]>([]);
  const [isEditing, setIsEditing] = useState(false);

  const toast = useRef<any>(null);
  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );

  // Helper function to check if a column should be visible
  const isColumnVisible = (field: string) => {
    // Mandatory columns are always visible
    if (mandatoryColumns.some((col) => col.field === field)) {
      return true;
    }
    // Check if toggleable column is in visible columns
    return visibleColumns.some((col) => col.field === field);
  };

  useEffect(() => {
    if (!isEditing) return;
    // If data already exists, use the cache and do not refetch.
    if (assets.length && vendors.length && vessels.length) return;

    async function loadData() {
      try {
        const [assetsData, vendorsData, vesselsData] = await Promise.all([
          fetchAssets(),
          fetchVendors(),
          fetchVessels(),
        ]);
        setAssets(assetsData);
        setVendors(vendorsData);
        setVessels(vesselsData);
      } catch (error) {
        console.error('Error loading lookup data', error);
      }
      setIsEditing(false);
    }

    loadData();
  }, [isEditing, assets, vendors, vessels]);

  useEffect(() => {
    // Trigger refetch when selected warehouse changes
    refetchItems();
  }, [selectedWarehouse, refetchItems]);

  const onRowEditInit = () => {
    setIsEditing(true);
  };

  const onRowEditComplete = async (e: any) => {
    const { newData } = e;
    try {
      await updateItem(newData.ItemId, newData);
      toast.current?.show(getUpdateSuccessToast());
      await refetchItems();
    } catch (error) {
      toast.current?.show(getUpdateErrorToast('Failed to update item'));
    } finally {
      setIsEditing(false);
    }
  };

  // Create the column toggle header
  const columnToggleHeader = onColumnToggle ? (
    <div className="flex justify-end items-center">
      <MultiSelect
        value={visibleColumns}
        options={toggleableColumns}
        optionLabel="header"
        onChange={onColumnToggle}
        className="w-80"
        display="chip"
        placeholder="Toggle columns"
        showClear
      />
    </div>
  ) : null;

  return (
    <>
      <DataTable<IItem[]>
        value={filteredReceipts}
        header={columnToggleHeader}
        paginator
        rows={20}
        rowsPerPageOptions={[10, 20, 50, 100]}
        stripedRows
        className="w-full"
        scrollable
        scrollHeight="60vh"
        selectionMode={userRole !== 'read' ? 'checkbox' : null}
        selection={userRole !== 'read' ? (selectedReceipts ?? []) : []}
        onSelectionChange={
          userRole !== 'read'
            ? (e: { value: IItem[] }) => setSelectedReceipts(e.value)
            : undefined
        }
        globalFilter={globalFilter}
        sortField="ReceiptDateTime"
        sortOrder={-1}
        size="small"
        rowHover
        globalFilterFields={[
          'ReceiptNumber',
          'PONumber',
          'SupplierDeliveryNoteNumber',
          'packageType',
          'SAPDelivery',
          'Manifest',
          'CCU',
          'Warehouse.Name',
          'Asset.Name',
          'Vendor.Name',
          'PONumber',
          'SupplierDeliveryNoteNumber',
          'ReceivedUser.Name',
          'Vessel.Name',
        ]}
        dataKey="ItemId"
        editMode="row"
        onRowEditInit={onRowEditInit}
        onRowEditComplete={onRowEditComplete}
      >
        {userRole !== 'read' && (
          <Column frozen selectionMode="multiple" style={{ width: '3rem' }} />
        )}

        <Column
          field="ReceiptNumber"
          header="Receipt Number"
          sortable
          editor={(options: {
            value: string;
            editorCallback?: (value: string) => void;
          }) => textEditor(options)}
          style={{ minWidth: '200px' }}
        />

        {isColumnVisible('ReceivedByUserId') && (
          <Column
            field="ReceivedByUserId"
            header="Received By"
            body={(rowData: IItem) => rowData.ReceivedUser?.Name}
            style={{ minWidth: '180px' }}
          />
        )}

        {isColumnVisible('ReceiptDateTime') && (
          <Column
            field="ReceiptDateTime"
            header="Receipt Date & Time"
            sortable
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => dateTimeEditor(options)}
            body={(rowData: IItem) => formatDateTime(rowData.ReceiptDateTime)}
            style={{ minWidth: '240px' }}
          />
        )}

        {isColumnVisible('AssetId') && (
          <Column
            field="AssetId"
            header="Asset"
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) =>
              dropdownEditor(
                options,
                assets,
                'Name',
                'AssetId',
                'Select an Asset'
              )
            }
            body={(rowData: IItem) => rowData.Asset?.Name}
            style={{ minWidth: '180px' }}
          />
        )}

        {isColumnVisible('VendorId') && (
          <Column
            field="VendorId"
            header="Vendor"
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) =>
              dropdownEditor(
                options,
                vendors,
                'Name',
                'VendorId',
                'Select a Vendor'
              )
            }
            body={(rowData: IItem) => rowData.Vendor?.Name}
            style={{ minWidth: '200px' }}
          />
        )}

        {isColumnVisible('PONumber') && (
          <Column
            field="PONumber"
            header="P/O Number"
            sortable
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => textEditor(options)}
            style={{ minWidth: '170px' }}
          />
        )}

        {isColumnVisible('SupplierDeliveryNoteNumber') && (
          <Column
            field="SupplierDeliveryNoteNumber"
            header="Supplier Delivery Note Number"
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => textEditor(options)}
            style={{
              minWidth: '200px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          />
        )}

        {isColumnVisible('SAPDelivery') && (
          <Column
            field="SAPDelivery"
            header="SAP Delivery"
            style={{ minWidth: '150px' }}
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => textEditor(options)}
          />
        )}

        {isColumnVisible('NumberOfPackages') && (
          <Column
            field="NumberOfPackages"
            header="# of Packages"
            editor={(options: {
              value: number;
              editorCallback?: (value: number) => void;
            }) => numberEditor(options)}
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
        )}

        {isColumnVisible('packageType') && (
          <Column
            field="packageType"
            header="Type of Package"
            body={(rowData) => (
              <PackageTypeTags packageType={rowData.packageType} />
            )}
            editor={(options: {
              value: string | string[];
              editorCallback?: (value: string[]) => void;
            }) =>
              multiSelectEditor(
                options,
                Object.values(PackageType).map((type) => ({
                  label: type,
                  value: type,
                })),
                'label',
                'value',
                'Select Package Types'
              )
            }
            style={{ minWidth: '200px' }}
          />
        )}

        {isColumnVisible('NumberOfLineItems') && (
          <Column
            field="NumberOfLineItems"
            header="# of Line Items"
            style={{ minWidth: '120px', textAlign: 'center' }}
            editor={(options: {
              value: number;
              editorCallback?: (value: number) => void;
            }) => numberEditor(options)}
          />
        )}

        {isColumnVisible('DescriptionOfGoods') && (
          <Column
            field="DescriptionOfGoods"
            header="Description of Goods"
            style={{ minWidth: '200px' }}
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => textEditor(options)}
          />
        )}

        {isColumnVisible('IsQuarantined') && (
          <Column
            field="IsQuarantined"
            header="Quarantined"
            editor={(options: {
              value: boolean;
              editorCallback?: (value: boolean) => void;
            }) => (
              <InputSwitch
                checked={options.value}
                onChange={(e) =>
                  options.editorCallback && options.editorCallback(e.value)
                }
              />
            )}
            body={(rowData: IItem) => (
              <i
                className={`pi pi-${
                  rowData.IsQuarantined ? 'lock' : 'unlock'
                } text-xl`}
                style={{
                  fontSize: '1.3rem',
                  color: rowData.IsQuarantined
                    ? 'var(--peterson-primary)'
                    : 'green',
                }}
              />
            )}
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
        )}

        {isColumnVisible('LoadListItem') && (
          <Column
            field="LoadListItem"
            header="Load List"
            editor={(options: {
              value: boolean;
              editorCallback?: (value: boolean) => void;
            }) => (
              <InputSwitch
                checked={options.value}
                onChange={(e) =>
                  options.editorCallback && options.editorCallback(e.value)
                }
              />
            )}
            body={(rowData: IItem) => (
              <i
                className={`pi pi-${
                  rowData.LoadListItem ? 'check-circle' : 'times-circle'
                } text-xl`}
                style={{
                  fontSize: '1.3rem',
                  color: rowData.LoadListItem
                    ? 'green'
                    : 'var(--peterson-primary)',
                }}
              />
            )}
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
        )}

        {isColumnVisible('CCU') && (
          <Column
            field="CCU"
            header="CCU"
            style={{ minWidth: '120px' }}
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => textEditor(options)}
          />
        )}

        {isColumnVisible('Manifest') && (
          <Column
            field="Manifest"
            header="Manifest"
            style={{ minWidth: '120px' }}
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => textEditor(options)}
          />
        )}

        {selectedWarehouse?.WarehouseId === 'all' && (
          <Column
            header="Warehouse"
            body={(rowData: IItem) => rowData.Warehouse?.Name}
            style={{ minWidth: '180px' }}
          />
        )}

        {isColumnVisible('SailingDate') && (
          <Column
            field="SailingDate"
            header="Sailing Date"
            body={(rowData: IItem) =>
              rowData.SailingDate ? formatDateTime(rowData.SailingDate) : ''
            }
            style={{ minWidth: '180px' }}
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) => dateTimeEditor(options)}
          />
        )}

        {isColumnVisible('VesselId') && (
          <Column
            field="VesselId"
            header="Vessel"
            editor={(options: {
              value: string;
              editorCallback?: (value: string) => void;
            }) =>
              dropdownEditor(
                options,
                vessels,
                'Name',
                'VesselId',
                'Select a Vessel'
              )
            }
            body={(rowData: IItem) => rowData.Vessel?.Name}
            style={{ minWidth: '180px' }}
          />
        )}

        {userRole !== 'read' && (
          <Column
            frozen
            header="Actions"
            alignFrozen="right"
            rowEditor
            headerStyle={{ width: '10%', minWidth: '8rem' }}
            bodyStyle={{ textAlign: 'center' }}
          />
        )}
      </DataTable>

      <ReusableToast ref={toast} />
    </>
  );
}

export default ReceiptsTable;
export { allColumns, toggleableColumns };
