'use client';
import { useState } from 'react';
import useSWR from 'swr';
import { fetchItems } from './receipts.services';
import { IItem } from '@/app/shared/models/global.interfaces';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';
import ReceiptsToolbar from './components/ReceiptsToolbar';
import ReceiptsTable, { allColumns } from './components/ReceiptsTable';
import { useAuthStore } from '@/store';

// ℹ️ Note. Receipts and Items are used interchangeably in the codebase.

function ReceiptsView() {
  const user = useAuthStore((state) => state.user);

  const {
    data: receipts,
    error,
    mutate,
    isLoading,
  } = useSWR(user ? '/items' : null, fetchItems);

  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [selectedReceipts, setSelectedReceipts] = useState<IItem[]>([]);
  const [filterQuarantined, setFilterQuarantined] = useState<boolean>(false);
  const [visibleColumns, setVisibleColumns] = useState(allColumns);

  // Handle column toggle
  const onColumnToggle = (event: any) => {
    let selectedColumns = event.value;
    let orderedSelectedColumns = allColumns.filter((col) =>
      selectedColumns.some((sCol: any) => sCol.field === col.field)
    );
    setVisibleColumns(orderedSelectedColumns);
  };

  // Compute filtered receipts based on the checkbox filter.
  const filteredReceipts: IItem[] =
    receipts && !error
      ? receipts
          .filter((item: IItem) => !filterQuarantined || item.IsQuarantined)
          .filter((item: IItem) => {
            if (!globalFilter) return true; // No filter applied, return all

            const searchTerm = globalFilter.toLowerCase();

            // Extract searchable fields, ensuring nested properties exist
            const fieldsToSearch = [
              item.ReceiptNumber,
              item.PONumber,
              item.SupplierDeliveryNoteNumber,
              item.packageType,
              item.SAPDelivery,
              item.Manifest,
              item.CCU,
              item.DescriptionOfGoods || '',
              item.Warehouse?.Name || '',
              item.Asset?.Name || '',
              item.Vendor?.Name || '',
              item.ReceivedUser?.Name || '',
              item.Vessel?.Name || '',
            ];

            return fieldsToSearch.some((field) =>
              field?.toString().toLowerCase().includes(searchTerm)
            );
          })
      : [];

  return (
    <div className="p-4">
      <Card className="shadow-lg p-4">
        {receipts && receipts.length === 0 && (
          <Message severity="info" text="No receipts found." />
        )}
        {error && <Message severity="error" text="Error loading receipts." />}
        {isLoading || !receipts ? (
          <div className="flex justify-center items-center py-10">
            <ProgressSpinner />
          </div>
        ) : (
          <>
            <ReceiptsToolbar
              selectedReceipts={selectedReceipts}
              globalFilter={globalFilter}
              setGlobalFilter={setGlobalFilter}
              filterQuarantined={filterQuarantined}
              setFilterQuarantined={setFilterQuarantined}
              refetchItems={mutate}
              userRole={user?.Role}
            />
            <ReceiptsTable
              filteredReceipts={filteredReceipts}
              selectedReceipts={selectedReceipts}
              setSelectedReceipts={setSelectedReceipts}
              globalFilter={globalFilter}
              refetchItems={mutate}
              userRole={user?.Role}
              visibleColumns={visibleColumns}
              onColumnToggle={onColumnToggle}
            />
          </>
        )}
      </Card>
    </div>
  );
}

export default ReceiptsView;
