/**
 * Utility functions for managing column visibility preferences in localStorage
 * with warehouse-specific storage keys
 */

export interface ColumnPreference {
  field: string;
  header: string;
}

export interface WarehouseColumnPreferences {
  [warehouseId: string]: {
    receipts?: string[]; // Array of field names for visible columns
    quarantine?: string[]; // Array of field names for visible columns
  };
}

// Storage keys
const COLUMN_PREFERENCES_KEY = 'peterson-column-preferences';
const DEFAULT_WAREHOUSE_KEY = 'all-warehouses';

/**
 * Get the storage key for a specific warehouse and table type
 */
function getStorageKey(
  warehouseId: string | undefined,
  tableType: 'receipts' | 'quarantine'
): string {
  const warehouse = warehouseId || DEFAULT_WAREHOUSE_KEY;
  return `${warehouse}-${tableType}`;
}

/**
 * Load all column preferences from localStorage
 */
function loadAllPreferences(): WarehouseColumnPreferences {
  try {
    const stored = localStorage.getItem(COLUMN_PREFERENCES_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn('Failed to load column preferences from localStorage:', error);
    return {};
  }
}

/**
 * Save all column preferences to localStorage
 */
function saveAllPreferences(preferences: WarehouseColumnPreferences): void {
  try {
    localStorage.setItem(COLUMN_PREFERENCES_KEY, JSON.stringify(preferences));
  } catch (error) {
    console.error('Failed to save column preferences to localStorage:', error);
  }
}

/**
 * Load column preferences for a specific warehouse and table type
 */
export function loadColumnPreferences(
  warehouseId: string | undefined,
  tableType: 'receipts' | 'quarantine',
  defaultColumns: ColumnPreference[]
): ColumnPreference[] {
  try {
    const allPreferences = loadAllPreferences();
    const warehouse = warehouseId || DEFAULT_WAREHOUSE_KEY;

    const warehousePrefs = allPreferences[warehouse];
    if (!warehousePrefs || !(tableType in warehousePrefs)) {
      // No saved preferences, return all default columns
      return defaultColumns;
    }

    const savedFieldNames = warehousePrefs[tableType];
    if (!savedFieldNames) {
      return defaultColumns;
    }

    // If explicitly saved as empty array, return empty (user cleared all)
    if (Array.isArray(savedFieldNames) && savedFieldNames.length === 0) {
      return [];
    }

    // Filter and order columns based on saved preferences
    const orderedColumns = savedFieldNames
      .map((fieldName) => defaultColumns.find((col) => col.field === fieldName))
      .filter(Boolean) as ColumnPreference[];

    return orderedColumns;
  } catch (error) {
    console.warn('Failed to load column preferences, using defaults:', error);
    return defaultColumns;
  }
}

/**
 * Save column preferences for a specific warehouse and table type
 */
export function saveColumnPreferences(
  warehouseId: string | undefined,
  tableType: 'receipts' | 'quarantine',
  visibleColumns: ColumnPreference[]
): void {
  try {
    const allPreferences = loadAllPreferences();
    const warehouse = warehouseId || DEFAULT_WAREHOUSE_KEY;

    // Initialize warehouse preferences if they don't exist
    if (!allPreferences[warehouse]) {
      allPreferences[warehouse] = {};
    }

    // Save the field names of visible columns
    allPreferences[warehouse][tableType] = visibleColumns.map(
      (col) => col.field
    );

    saveAllPreferences(allPreferences);
  } catch (error) {
    console.error('Failed to save column preferences:', error);
  }
}

/**
 * Clear column preferences for a specific warehouse and table type
 */
export function clearColumnPreferences(
  warehouseId: string | undefined,
  tableType: 'receipts' | 'quarantine'
): void {
  try {
    const allPreferences = loadAllPreferences();
    const warehouse = warehouseId || DEFAULT_WAREHOUSE_KEY;

    if (allPreferences[warehouse]) {
      delete allPreferences[warehouse][tableType];

      // If warehouse has no preferences left, remove it entirely
      if (Object.keys(allPreferences[warehouse]).length === 0) {
        delete allPreferences[warehouse];
      }
    }

    saveAllPreferences(allPreferences);
  } catch (error) {
    console.error('Failed to clear column preferences:', error);
  }
}

/**
 * Get all saved warehouse IDs that have column preferences
 */
export function getSavedWarehouseIds(): string[] {
  try {
    const allPreferences = loadAllPreferences();
    return Object.keys(allPreferences);
  } catch (error) {
    console.warn('Failed to get saved warehouse IDs:', error);
    return [];
  }
}

/**
 * Debug function to log all saved preferences
 */
export function debugColumnPreferences(): void {
  console.log('All column preferences:', loadAllPreferences());
}
