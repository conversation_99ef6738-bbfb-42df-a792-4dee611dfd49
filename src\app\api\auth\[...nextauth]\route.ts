// @check copied from comet-stats-app

import NextAuth, { NextAuthOptions } from 'next-auth';
import AzureADProvider from 'next-auth/providers/azure-ad';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/prisma';

// Type declarations for extended functionality
declare module 'next-auth' {
  interface Session {
    accessToken?: string;
    idToken?: string;
    user: {
      id?: string;
      name?: string;
      email?: string;
      image?: string;
      role?: string;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    provider?: string;
    accessToken?: string;
    idToken?: string;
    role?: string;
    userId?: string;
    email?: string;
    name?: string;
  }
}

// Cookie configuration
const COOKIE_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
  maxAge: 30 * 24 * 60 * 60, // 30 days
} as const;

// Auth configuration options
const authOptions: NextAuthOptions = {
  providers: [
    AzureADProvider({
      clientId: process.env.AZURE_AD_CLIENT_ID!,
      clientSecret: process.env.AZURE_AD_CLIENT_SECRET!,
      tenantId: process.env.AZURE_AD_TENANT_ID!,
      authorization: {
        params: {
          scope: 'openid profile user.Read email',
        },
      },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET || 'peterson-receipt-register-secret',
  pages: {
    signIn: '/login',
    signOut: '/login',
    error: '/login',
  },
  callbacks: {
    async signIn({ user }) {
      console.log(`Azure AD login attempt with email: "${user.email}"`);
      if (user?.email) {
        // // Log the exact email from Azure AD for debugging

        // // First, let's log all users with similar emails to debug the issue
        // const allUsers = await prisma.user.findMany({
        //   where: {
        //     Email: {
        //       contains: '@onepeterson.com',
        //     },
        //   },
        //   select: {
        //     UserId: true,
        //     Name: true,
        //     Email: true,
        //   },
        // });

        // console.log(
        //   'All users with onepeterson.com domain:',
        //   JSON.stringify(allUsers, null, 2)
        // );

        // Check if the user exists in our database and is not deleted
        // Using exact email match and ensuring it's not deleted
        const dbUser = await prisma.user.findFirst({
          where: {
            Email: user.email as string,
            IsDeleted: false,
          },
        });

        if (!dbUser) {
          console.log(`No matching user found for email: "${user.email}"`);
          return false; // This will trigger the signIn callback to fail
        }

        console.log(
          `Found matching user: ${dbUser.Name} (${dbUser.Email}) with ID: ${dbUser.UserId}`
        );
        console.log(
          `Email comparison: Azure AD "${user.email}" vs Database "${dbUser.Email}"`
        );

        return true;
      }
      return false; // Allow sign in
    },
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        console.log(`JWT callback for user email: "${user.email}"`);

        // Get user from our database with exact email match
        const dbUser = await prisma.user.findFirst({
          where: {
            Email: user.email as string,
            IsDeleted: false,
          },
        });

        // We should never reach here with invalid users due to signIn callback,
        // but adding this as an extra safety measure
        if (!dbUser) {
          console.log(
            `JWT callback: No matching user found for email: "${user.email}"`
          );
          return {
            ...token,
            error: 'AccessDenied',
          };
        }

        console.log(
          `JWT callback: Found matching user: ${dbUser.Name} (${dbUser.Email}) with ID: ${dbUser.UserId}`
        );
        console.log(
          `JWT email comparison: Azure AD "${user.email}" vs Database "${dbUser.Email}"`
        );

        // Use the email from the database to ensure consistency
        return {
          ...token,
          userId: dbUser.UserId,
          name: dbUser.Name,
          email: dbUser.Email,
          role: dbUser.Role,
        };
      }
      return token;
    },
    async session({ session, token }) {
      // If there's an error in the token, don't create a session
      if (token.error === 'AccessDenied') {
        // Use a standard NextAuth error that will be properly passed to the error page
        throw new Error('AccessDenied');
      }

      if (token) {
        console.log(
          `Session callback: Using token data - userId: ${token.userId}, email: "${token.email}"`
        );

        session.user.id = token.userId;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.role = token.role;
      }

      try {
        const cookieStore = await cookies();
        if (session.idToken) {
          cookieStore.set('idToken', session.idToken, COOKIE_CONFIG);
        }

        // Also set user email in a cookie for API routes
        if (session.user.email) {
          console.log(
            `Setting email cookie with value: "${session.user.email}"`
          );
          cookieStore.set('userEmail', session.user.email, COOKIE_CONFIG);
        }

        // Set user role in a cookie for API routes
        if (session.user.role) {
          cookieStore.set('userRole', session.user.role, COOKIE_CONFIG);
        }
      } catch (error) {
        console.error('Failed to set authentication cookies:', error);
      }
      return session;
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
