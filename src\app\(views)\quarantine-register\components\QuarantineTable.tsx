import { useState, useEffect, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { formatDateTime } from '@/app/shared/utils/dates.utils';
import {
  fetchAssets,
  fetchVendors,
  updateQuarantineDetails,
} from '@/app/(views)/receipts/receipts.services';
import { IItem } from '@/app/shared/models/global.interfaces';
import { InputSwitch } from 'primereact/inputswitch';
import {
  dateTimeEditor,
  textEditor,
  dropdownEditor,
  multiSelectEditor,
} from '../../receipts/utils/editors.utils';
import ReusableToast from '@/app/shared/components/ReusableToast';
import {
  getUpdateSuccessToast,
  getUpdateErrorToast,
} from '../../receipts/components/utils/ReceiptsDialogsSettings';
import { KeyedMutator } from 'swr';
import {
  QuarantineStatus,
  PackageType,
} from '@/app/shared/models/global.enums';
import { useWarehouseStore } from '@/store';
import PackageTypeTags from '@/app/shared/components/PackageTypeTags';

function calculateElapsedDays(
  receiptDateTime: Date | string | null | undefined,
  dateClosed: Date | string | null | undefined
): number {
  if (!receiptDateTime || !dateClosed) return 0;

  const receiptDate = new Date(receiptDateTime);
  const closedDate = new Date(dateClosed);

  if (isNaN(receiptDate.getTime()) || isNaN(closedDate.getTime())) return 0;

  // Calculate difference in days
  const diffTime = closedDate.getTime() - receiptDate.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Convert ms to days
}

// Define all available columns for the quarantine table
const allQuarantineColumns = [
  { field: 'ReceiptNumber', header: 'Receipt Number' },
  { field: 'ReceivedByUserId', header: 'Received By' },
  { field: 'ReceiptDateTime', header: 'Receipt Date & Time' },
  { field: 'AssetId', header: 'Asset' },
  { field: 'VendorId', header: 'Vendor' },
  { field: 'PONumber', header: 'P/O Number' },
  {
    field: 'SupplierDeliveryNoteNumber',
    header: 'Supplier Delivery Note Number',
  },
  { field: 'packageType', header: 'Type of Package' },
  { field: 'NumberOfPackages', header: '# of Packages' },
  {
    field: 'DescriptionOfNonConformance',
    header: 'Description of Non Conformance',
  },
  { field: 'NCRNumber', header: 'NCR Number' },
  { field: 'Status', header: 'Status' },
  { field: 'DateClosed', header: 'Date Closed' },
  { field: 'ElapsedDays', header: 'Elapsed Time (days)' },
  { field: 'IsQuarantined', header: 'Quarantined' },
];

interface QuarantineTableProps {
  quarantinedItems: IItem[];
  refetchItems: KeyedMutator<IItem[]>;
  userRole?: string;
  visibleColumns?: any[];
  onColumnToggle?: (event: any) => void;
}

function QuarantineTable({
  quarantinedItems,
  refetchItems,
  userRole,
  visibleColumns = allQuarantineColumns,
  onColumnToggle,
}: QuarantineTableProps) {
  // Lookup data states
  const [assets, setAssets] = useState<any[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [_isEditing, setIsEditing] = useState(false);
  const toast = useRef<any>(null);
  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );

  // Helper function to check if a column should be visible
  const isColumnVisible = (field: string) => {
    return visibleColumns.some((col) => col.field === field);
  };

  useEffect(() => {
    async function loadData() {
      try {
        const [assetsData, vendorsData] = await Promise.all([
          fetchAssets(),
          fetchVendors(),
        ]);
        setAssets(assetsData);
        setVendors(vendorsData);
      } catch (error) {
        console.error('Error loading lookup data', error);
      }
    }
    loadData();
  }, []);

  useEffect(() => {
    // Trigger refetch when selected warehouse changes
    refetchItems();
  }, [selectedWarehouse, refetchItems]);

  const onRowEditInit = () => {
    setIsEditing(true);
  };

  const onRowEditComplete = async (e: any) => {
    const { newData } = e;
    try {
      await updateQuarantineDetails(newData.ItemId, {
        ReceiptNumber: newData.ReceiptNumber,
        ReceiptDateTime: newData.ReceiptDateTime,
        AssetId: newData.AssetId,
        VendorId: newData.VendorId,
        DescriptionOfNonConformance: newData.DescriptionOfNonConformance,
        NCRNumber: newData.NCRNumber,
        Status: newData.Status,
        DateClosed: newData.DateClosed,
        IsQuarantined: newData.IsQuarantined,
        PONumber: newData.PONumber,
        SupplierDeliveryNoteNumber: newData.SupplierDeliveryNoteNumber,
        packageType: newData.packageType,
        NumberOfPackages: newData.NumberOfPackages,
      });

      toast.current?.show(getUpdateSuccessToast());

      await refetchItems();
    } catch (error) {
      console.error('Error updating quarantine item:', error);
      toast.current?.show(
        getUpdateErrorToast('Failed to update quarantine item')
      );
    } finally {
      setIsEditing(false);
    }
  };

  // Create the column toggle header
  const columnToggleHeader = onColumnToggle ? (
    <div className="flex justify-between items-center">
      <span>Columns</span>
      <MultiSelect
        value={visibleColumns}
        options={allQuarantineColumns}
        optionLabel="header"
        onChange={onColumnToggle}
        className="w-full sm:w-20rem"
        display="chip"
        placeholder="Select columns to display"
      />
    </div>
  ) : null;

  return (
    <>
      <DataTable
        value={quarantinedItems}
        header={columnToggleHeader}
        paginator
        rows={20}
        rowsPerPageOptions={[10, 20, 50, 100]}
        stripedRows
        // showGridlines
        className="w-full"
        scrollable
        scrollHeight="70vh"
        size="small"
        rowHover
        sortField="ReceiptDateTime"
        sortOrder={-1}
        editMode="row"
        onRowEditInit={onRowEditInit}
        onRowEditComplete={onRowEditComplete}
      >
        {isColumnVisible('ReceiptNumber') && (
          <Column
            field="ReceiptNumber"
            header="Receipt Number"
            sortable
            style={{ minWidth: '200px' }}
            editor={(options) => textEditor(options)}
          />
        )}

        {isColumnVisible('ReceivedByUserId') && (
          <Column
            field="ReceivedByUserId"
            header="Received By"
            body={(rowData: IItem) => rowData.ReceivedUser?.Name}
            style={{ minWidth: '180px' }}
          />
        )}

        {isColumnVisible('ReceiptDateTime') && (
          <Column
            field="ReceiptDateTime"
            header="Receipt Date & Time"
            body={(rowData: IItem) => formatDateTime(rowData.ReceiptDateTime)}
            sortable
            style={{ minWidth: '240px', textAlign: 'left' }}
            editor={(options) => dateTimeEditor(options)}
          />
        )}

        {isColumnVisible('AssetId') && (
          <Column
            field="AssetId"
            header="Asset"
            body={(rowData: IItem) => rowData.Asset?.Name}
            style={{ minWidth: '180px' }}
            editor={(options) =>
              dropdownEditor(
                options,
                assets,
                'Name',
                'AssetId',
                'Select an Asset'
              )
            }
          />
        )}

        {isColumnVisible('VendorId') && (
          <Column
            field="VendorId"
            header="Vendor"
            body={(rowData: IItem) => rowData.Vendor?.Name}
            style={{ minWidth: '200px' }}
            editor={(options) =>
              dropdownEditor(
                options,
                vendors,
                'Name',
                'VendorId',
                'Select a Vendor'
              )
            }
          />
        )}

        {isColumnVisible('PONumber') && (
          <Column
            field="PONumber"
            header="P/O Number"
            sortable
            style={{ minWidth: '170px' }}
            editor={(options) => textEditor(options)}
          />
        )}

        {isColumnVisible('SupplierDeliveryNoteNumber') && (
          <Column
            field="SupplierDeliveryNoteNumber"
            header="Supplier Delivery Note Number"
            style={{
              minWidth: '200px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
            editor={(options) => textEditor(options)}
          />
        )}

        {isColumnVisible('packageType') && (
          <Column
            field="packageType"
            header="Type of Package"
            style={{ minWidth: '200px' }}
            body={(rowData) => (
              <PackageTypeTags packageType={rowData.packageType} />
            )}
            editor={(options) =>
              multiSelectEditor(
                options,
                Object.values(PackageType).map((type) => ({
                  label: type,
                  value: type,
                })),
                'label',
                'value',
                'Select Package Types'
              )
            }
          />
        )}

        {isColumnVisible('NumberOfPackages') && (
          <Column
            field="NumberOfPackages"
            header="# of Packages"
            style={{ minWidth: '120px', textAlign: 'center' }}
            editor={(options) => textEditor(options)}
          />
        )}

        {isColumnVisible('DescriptionOfNonConformance') && (
          <Column
            field="DescriptionOfNonConformance"
            header="Description of Non Conformance"
            style={{ minWidth: '250px' }}
            editor={(options) => textEditor(options)}
          />
        )}

        {isColumnVisible('NCRNumber') && (
          <Column
            field="NCRNumber"
            header="NCR Number"
            style={{ minWidth: '130px' }}
            editor={(options) => textEditor(options)}
          />
        )}

        {isColumnVisible('Status') && (
          <Column
            field="Status"
            header="Status"
            style={{ minWidth: '130px' }}
            editor={(options) =>
              dropdownEditor(
                options,
                Object.values(QuarantineStatus).map((status) => ({
                  label: status,
                  value: status,
                })),
                'label',
                'value',
                'Select a status'
              )
            }
          />
        )}

        {isColumnVisible('DateClosed') && (
          <Column
            field="DateClosed"
            header="Date Closed"
            body={(rowData: IItem) =>
              rowData.DateClosed ? formatDateTime(rowData.DateClosed) : ''
            }
            style={{ minWidth: '190px' }}
            editor={(options) => dateTimeEditor(options)}
          />
        )}

        {isColumnVisible('ElapsedDays') && (
          <Column
            field="ElapsedDays"
            header="Elapsed Time (days)"
            body={(rowData: IItem) =>
              calculateElapsedDays(rowData.ReceiptDateTime, rowData.DateClosed)
            }
            style={{ minWidth: '150px', textAlign: 'center' }}
          />
        )}

        {isColumnVisible('IsQuarantined') && (
          <Column
            frozen
            alignFrozen="right"
            field="IsQuarantined"
            header="Quarantined"
            style={{ minWidth: '150px', textAlign: 'center' }}
            body={(rowData: IItem) => (
              <i
                className={`pi pi-${rowData.IsQuarantined ? 'lock' : 'unlock'} text-xl`}
                style={{
                  fontSize: '1.3rem',
                  color: rowData.IsQuarantined
                    ? 'var(--peterson-primary)'
                    : 'green',
                }}
              />
            )}
            editor={(options) => (
              <InputSwitch
                checked={options.value}
                onChange={(e) => options.editorCallback?.(e.value)}
              />
            )}
          />
        )}

        {userRole !== 'read' && (
          <Column
            header="Actions"
            frozen
            alignFrozen="right"
            rowEditor
            headerStyle={{
              width: '10%',
              minWidth: '8rem',
              pointerEvents: 'none',
            }}
            bodyStyle={{ textAlign: 'center' }}
          />
        )}
      </DataTable>
      <ReusableToast ref={toast} />
    </>
  );
}

export default QuarantineTable;
export { allQuarantineColumns };
