// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model Asset {
  AssetId        String           @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  Name           String           @unique
  IsDeleted      Boolean          @default(false)
  Items          Item[]
  AssetWarehouse AssetWarehouse[]
}

model Vendor {
  VendorId        String            @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  Name            String            @unique
  IsDeleted       Boolean           @default(false)
  Items           Item[]
  VendorWarehouse VendorWarehouse[]
}

model Warehouse {
  WarehouseId     String            @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  Name            String
  IsDeleted       Boolean           @default(false)
  Items           Item[]
  Users           UserWarehouse[]
  AssetWarehouse  AssetWarehouse[]
  VesselWarehouse VesselWarehouse[]
  VendorWarehouse VendorWarehouse[]
}

model User {
  UserId     String          @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  Name       String
  Email      String          @unique
  Role       String
  IsDeleted  Boolean         @default(false)
  Items      Item[]
  Warehouses UserWarehouse[]
}

model UserWarehouse {
  UserWarehouseId String    @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  UserId          String    @db.UniqueIdentifier
  WarehouseId     String    @db.UniqueIdentifier
  User            User      @relation(fields: [UserId], references: [UserId])
  Warehouse       Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model Vessel {
  VesselId        String            @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  Name            String            @unique
  IsDeleted       Boolean           @default(false)
  Items           Item[]
  VesselWarehouse VesselWarehouse[]
}

model AssetWarehouse {
  AssetWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  AssetId          String @db.UniqueIdentifier
  WarehouseId      String @db.UniqueIdentifier

  Asset     Asset     @relation(fields: [AssetId], references: [AssetId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model VesselWarehouse {
  VesselWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  VesselId          String @db.UniqueIdentifier
  WarehouseId       String @db.UniqueIdentifier

  Vessel    Vessel    @relation(fields: [VesselId], references: [VesselId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model VendorWarehouse {
  VendorWarehouseId String @id @default(dbgenerated("(newid())")) @db.UniqueIdentifier
  VendorId          String @db.UniqueIdentifier
  WarehouseId       String @db.UniqueIdentifier

  Vendor    Vendor    @relation(fields: [VendorId], references: [VendorId])
  Warehouse Warehouse @relation(fields: [WarehouseId], references: [WarehouseId])
}

model Item {
  ItemId                      String     @id @default(dbgenerated("newid()")) @db.UniqueIdentifier
  WarehouseId                 String?    @db.UniqueIdentifier
  ReceivedByUserId            String     @db.UniqueIdentifier
  AssetId                     String?    @db.UniqueIdentifier
  VendorId                    String     @db.UniqueIdentifier
  ReceiptNumber               String     @unique
  ReceiptDateTime             DateTime
  SailingDate                 DateTime?
  PONumber                    String?
  SupplierDeliveryNoteNumber  String?
  NumberOfPackages            Int
  NumberOfLineItems           Int?
  packageType                 String
  SAPDelivery                 String?
  DescriptionOfGoods          String?
  IsQuarantined               Boolean    @default(false)
  LoadListItem                Boolean    @default(false)
  CCU                         String?
  Manifest                    String?
  IsDeleted                   Boolean    @default(false)
  VesselId                    String?    @db.UniqueIdentifier
  DateClosed                  DateTime?
  ElapsedTimeDays             Int?
  NCRNumber                   String?
  Status                      String?
  DescriptionOfNonConformance String?
  Asset                       Asset?     @relation(fields: [AssetId], references: [AssetId], onDelete: NoAction, onUpdate: NoAction)
  ReceivedUser                User       @relation(fields: [ReceivedByUserId], references: [UserId], onUpdate: NoAction)
  Vendor                      Vendor     @relation(fields: [VendorId], references: [VendorId], onUpdate: NoAction)
  Vessel                      Vessel?    @relation(fields: [VesselId], references: [VesselId], onDelete: NoAction, onUpdate: NoAction)
  Warehouse                   Warehouse? @relation(fields: [WarehouseId], references: [WarehouseId], onDelete: NoAction, onUpdate: NoAction)
}
