BEGIN TRY

BEGIN TRAN;

-- CreateTable
CREATE TABLE [dbo].[AssetWarehouse] (
    [Asset<PERSON>arehouseId] UNIQUEIDENTIFIER NOT NULL CONSTRAINT [AssetWarehouse_AssetWarehouseId_df] DEFAULT (newid()),
    [AssetId] UNIQUEIDENTIFIER NOT NULL,
    [WarehouseId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [Asset<PERSON>arehouse_pkey] PRIMARY KEY CLUSTERED ([AssetWarehouseId])
);

-- CreateTable
CREATE TABLE [dbo].[VesselWarehouse] (
    [VesselWarehouseId] UNIQUEIDENTIFIER NOT NULL CONSTRAINT [VesselWarehouse_VesselWarehouseId_df] DEFAULT (newid()),
    [VesselId] UNIQUEIDENTIFIER NOT NULL,
    [WarehouseId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [VesselWarehouse_pkey] PRIMARY KEY CLUSTERED ([Vessel<PERSON>arehouseId])
);

-- CreateTable
CREATE TABLE [dbo].[VendorWarehouse] (
    [VendorWarehouseId] UNIQUEIDENTIFIER NOT NULL CONSTRAINT [VendorWarehouse_VendorWarehouseId_df] DEFAULT (newid()),
    [VendorId] UNIQUEIDENTIFIER NOT NULL,
    [WarehouseId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [VendorWarehouse_pkey] PRIMARY KEY CLUSTERED ([VendorWarehouseId])
);

-- AddForeignKey
ALTER TABLE [dbo].[AssetWarehouse] ADD CONSTRAINT [AssetWarehouse_AssetId_fkey] FOREIGN KEY ([AssetId]) REFERENCES [dbo].[Asset]([AssetId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[AssetWarehouse] ADD CONSTRAINT [AssetWarehouse_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[VesselWarehouse] ADD CONSTRAINT [VesselWarehouse_VesselId_fkey] FOREIGN KEY ([VesselId]) REFERENCES [dbo].[Vessel]([VesselId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[VesselWarehouse] ADD CONSTRAINT [VesselWarehouse_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[VendorWarehouse] ADD CONSTRAINT [VendorWarehouse_VendorId_fkey] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendor]([VendorId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[VendorWarehouse] ADD CONSTRAINT [VendorWarehouse_WarehouseId_fkey] FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouse]([WarehouseId]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
