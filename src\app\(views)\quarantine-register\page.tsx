'use client';

import { useState } from 'react';
import useS<PERSON> from 'swr';
import { fetchItems } from '../receipts/receipts.services';
import { IItem } from '@/app/shared/models/global.interfaces';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';
import QuarantineTable, {
  allQuarantineColumns,
} from './components/QuarantineTable';
import QuarantineToolbar from './components/QuarantineToolbar';
import { useAuthStore } from '@/store';

const QuarantineRegister = () => {
  const user = useAuthStore((state) => state.user);

  const {
    data: items = [],
    error,
    mutate: refetchItems,
  } = useSWR(user ? '/items' : null, fetchItems);

  const [globalFilter, setGlobalFilter] = useState('');
  const [visibleColumns, setVisibleColumns] = useState(allQuarantineColumns);

  // Handle column toggle
  const onColumnToggle = (event: any) => {
    let selectedColumns = event.value;
    let orderedSelectedColumns = allQuarantineColumns.filter((col) =>
      selectedColumns.some((sCol: any) => sCol.field === col.field)
    );
    setVisibleColumns(orderedSelectedColumns);
  };

  // Filter items to only show quarantined items
  const quarantinedItems = items.filter((item: IItem) => item.IsQuarantined);

  // Filter based on search input
  const filteredQuarantineItems = globalFilter
    ? quarantinedItems.filter((item: IItem) => {
        const searchFields = [
          item.ReceiptNumber,
          item.PONumber,
          item.SupplierDeliveryNoteNumber,
          item.packageType,
          item.ReceivedUser?.Name,
          item.Asset?.Name,
          item.Vendor?.Name,
          item.NCRNumber,
          item.IsQuarantined,
        ];

        return searchFields.some(
          (field) =>
            typeof field === 'string' &&
            field.toLowerCase().includes(globalFilter.toLowerCase())
        );
      })
    : quarantinedItems;

  if (error)
    return <Message severity="error" text="Failed to load quarantine data" />;
  if (!items) {
    return (
      <div className="flex justify-center items-center py-10">
        <ProgressSpinner />
      </div>
    );
  }

  return (
    <div className="p-4">
      <Card className="shadow-lg p-4">
        <QuarantineToolbar
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
        />
        <QuarantineTable
          quarantinedItems={filteredQuarantineItems}
          refetchItems={refetchItems}
          userRole={user?.Role}
          visibleColumns={visibleColumns}
          onColumnToggle={onColumnToggle}
        />
      </Card>
    </div>
  );
};

export default QuarantineRegister;
