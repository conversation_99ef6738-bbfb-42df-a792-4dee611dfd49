'use client';

import { useState, useEffect } from 'react';
import useSWR from 'swr';
import { fetchItems } from '../receipts/receipts.services';
import { IItem } from '@/app/shared/models/global.interfaces';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';
import QuarantineTable, {
  toggleableQuarantineColumns,
} from './components/QuarantineTable';
import QuarantineToolbar from './components/QuarantineToolbar';
import { useAuthStore, useWarehouseStore } from '@/store';
import {
  loadColumnPreferences,
  saveColumnPreferences,
} from '@/app/shared/utils/columnPreferences.utils';

const QuarantineRegister = () => {
  const user = useAuthStore((state) => state.user);
  const selectedWarehouse = useWarehouseStore(
    (state) => state.selectedWarehouse
  );

  const {
    data: items = [],
    error,
    mutate: refetchItems,
  } = useSWR(user ? '/items' : null, fetchItems);

  const [globalFilter, setGlobalFilter] = useState('');
  const [visibleColumns, setVisibleColumns] = useState<any[]>([]);

  // Load column preferences when warehouse changes
  useEffect(() => {
    const warehouseId =
      selectedWarehouse?.WarehouseId === 'all'
        ? undefined
        : selectedWarehouse?.WarehouseId;
    const savedColumns = loadColumnPreferences(
      warehouseId,
      'quarantine',
      toggleableQuarantineColumns
    );
    setVisibleColumns(savedColumns);
  }, [selectedWarehouse]);

  // Handle column toggle
  const onColumnToggle = (event: any) => {
    let selectedColumns = event.value;
    let orderedSelectedColumns = toggleableQuarantineColumns.filter((col) =>
      selectedColumns.some((sCol: any) => sCol.field === col.field)
    );
    setVisibleColumns(orderedSelectedColumns);

    // Save preferences to localStorage
    const warehouseId =
      selectedWarehouse?.WarehouseId === 'all'
        ? undefined
        : selectedWarehouse?.WarehouseId;
    saveColumnPreferences(warehouseId, 'quarantine', orderedSelectedColumns);
  };

  // Filter items to only show quarantined items
  const quarantinedItems = items.filter((item: IItem) => item.IsQuarantined);

  // Filter based on search input
  const filteredQuarantineItems = globalFilter
    ? quarantinedItems.filter((item: IItem) => {
        const searchFields = [
          item.ReceiptNumber,
          item.PONumber,
          item.SupplierDeliveryNoteNumber,
          item.packageType,
          item.ReceivedUser?.Name,
          item.Asset?.Name,
          item.Vendor?.Name,
          item.NCRNumber,
          item.IsQuarantined,
        ];

        return searchFields.some(
          (field) =>
            typeof field === 'string' &&
            field.toLowerCase().includes(globalFilter.toLowerCase())
        );
      })
    : quarantinedItems;

  if (error)
    return <Message severity="error" text="Failed to load quarantine data" />;
  if (!items) {
    return (
      <div className="flex justify-center items-center py-10">
        <ProgressSpinner />
      </div>
    );
  }

  return (
    <div className="p-4">
      <Card className="shadow-lg p-4">
        <QuarantineToolbar
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
        />
        <QuarantineTable
          quarantinedItems={filteredQuarantineItems}
          refetchItems={refetchItems}
          userRole={user?.Role}
          visibleColumns={visibleColumns}
          onColumnToggle={onColumnToggle}
          warehouseId={selectedWarehouse?.WarehouseId}
        />
      </Card>
    </div>
  );
};

export default QuarantineRegister;
